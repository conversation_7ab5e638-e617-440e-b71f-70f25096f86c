import React, { useState } from 'react';
import { Play, RefreshCw, CheckCircle, XCircle, AlertCircle, Loader2, Clock } from 'lucide-react';

const TemuWorkflowDemo = () => {
  const [currentStep, setCurrentStep] = useState(-1); // -1表示未开始
  const [isRunning, setIsRunning] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState('idle'); // idle, running, completed, failed
  const [failedStep, setFailedStep] = useState(null); // 记录失败的步骤
  const [executionResults, setExecutionResults] = useState(null); // 执行结果详情

  // 工作流步骤定义 - 增强版本，包含失败详情
  const workflowSteps = [
    {
      id: 'login',
      name: '登录验证',
      runningDescription: '正在验证Temu账号登录状态...',
      completedDescription: '已成功登录【Temu账号】名下的店铺1',
      failedDescription: '登录验证失败，请检查账号状态',
      duration: 2000,
      status: 'waiting'
    },
    {
      id: 'product_filter',
      name: '商品筛选',
      runningDescription: '正在筛选符合条件的商品...',
      completedDescription: '已筛选出【Temu账号】名下的店铺1，普通流量加速特权≥$5的商品，共200件商品',
      failedDescription: '商品筛选失败，可能是网络连接问题',
      duration: 3000,
      status: 'waiting'
    },
    {
      id: 'product_processing',
      name: '商品加权处理',
      runningDescription: '正在为商品设置流量加权配置...',
      completedDescription: '已完成对筛选出的商品，设置30天的普通流量加速加权',
      failedDescription: '商品加权处理失败，部分商品无法配置',
      duration: 4000,
      status: 'waiting'
    },
    {
      id: 'result_summary',
      name: '结果汇总',
      runningDescription: '正在生成执行结果报告...',
      completedDescription: '任务执行完成，正在生成详细报告',
      failedDescription: '结果汇总失败，无法生成完整报告',
      duration: 1500,
      status: 'waiting'
    }
  ];

  const [steps, setSteps] = useState(workflowSteps);

  // 获取步骤描述
  const getStepDescription = (step) => {
    switch (step.status) {
      case 'running':
        return step.runningDescription;
      case 'completed':
        return step.completedDescription;
      case 'failed':
        return step.failedDescription;
      case 'waiting':
        return '等待执行...';
      default:
        return '等待执行...';
    }
  };

  // 生成详细的执行结果报告
  const generateExecutionReport = () => {
    const successfulProducts = [
      '【智能手机壳-透明款】', '【蓝牙耳机-运动版】', '【充电宝-快充版】',
      '【数据线-Type-C】', '【手机支架-桌面版】', '【无线充电器】'
    ];

    const failedProducts = [
      { name: '【高端手机壳-奢华版】', reason: 'AI判断其价格为6.8美元，高于5美元的设定而被跳过' },
      { name: '【专业摄影灯】', reason: '页面长时间无法打开而执行失败' }
    ];

    const report = {
      totalProcessed: 200,
      successful: 186,
      failed: 14,
      totalCost: 892.40,
      successfulProducts: successfulProducts.slice(0, 6),
      failedProducts: failedProducts,
      executionTime: '8分32秒'
    };

    return report;
  };

  // 模拟工作流执行 - 重新设计的执行逻辑
  const executeWorkflow = async () => {
    setIsRunning(true);
    setWorkflowStatus('running');
    setExecutionResults(null);

    // 从失败步骤开始，或从第一步开始
    const startStep = failedStep !== null ? failedStep : 0;
    setCurrentStep(startStep);

    // 如果是全新开始，重置所有步骤状态
    if (failedStep === null) {
      setSteps(prev => prev.map(step => ({ ...step, status: 'waiting' })));
    } else {
      // 如果是重试，只重置失败步骤的状态
      setSteps(prev => prev.map((step, index) =>
        index === failedStep ? { ...step, status: 'waiting' } : step
      ));
      setFailedStep(null);
    }

    // 执行工作流步骤
    for (let i = startStep; i < steps.length; i++) {
      setCurrentStep(i);

      // 设置当前步骤为运行中
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'running' } : step
      ));

      // 模拟步骤执行时间
      await new Promise(resolve => setTimeout(resolve, steps[i].duration));

      // 模拟随机失败（20%概率，主要在第2和第3步）
      const shouldFail = Math.random() < 0.2 && (i === 1 || i === 2);

      if (shouldFail) {
        // 步骤失败
        setSteps(prev => prev.map((step, index) =>
          index === i ? { ...step, status: 'failed' } : step
        ));
        setWorkflowStatus('failed');
        setFailedStep(i);
        setIsRunning(false);
        return;
      }

      // 步骤成功完成
      setSteps(prev => prev.map((step, index) =>
        index === i ? { ...step, status: 'completed' } : step
      ));

      // 短暂延迟，让用户看到状态变化
      await new Promise(resolve => setTimeout(resolve, 300));
    }

    // 所有步骤完成，生成执行结果
    const report = generateExecutionReport();
    setExecutionResults(report);
    setWorkflowStatus('completed');
    setFailedStep(null);
    setIsRunning(false);
    setCurrentStep(-1);
  };

  // 重试失败的步骤 - 精准重试机制
  const retryFromFailed = () => {
    if (failedStep !== null && !isRunning) {
      executeWorkflow();
    }
  };

  // 重置整个工作流
  const resetWorkflow = () => {
    setIsRunning(false);
    setWorkflowStatus('idle');
    setCurrentStep(-1);
    setFailedStep(null);
    setExecutionResults(null);
    setSteps(workflowSteps.map(step => ({ ...step, status: 'waiting' })));
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* 长任务执行监控面板 */}
      <div className="bg-white/95 backdrop-blur-sm rounded-2xl shadow-xl overflow-hidden border border-white/30">
        {/* 头部状态栏 */}
        <div className={`p-4 transition-all duration-500 ${
          workflowStatus === 'running' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
          workflowStatus === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' :
          workflowStatus === 'failed' ? 'bg-gradient-to-r from-red-500 to-red-600' :
          'bg-gradient-to-r from-gray-500 to-gray-600'
        } text-white`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                {workflowStatus === 'running' && <Loader2 className="w-5 h-5 animate-spin" />}
                {workflowStatus === 'completed' && <CheckCircle className="w-5 h-5" />}
                {workflowStatus === 'failed' && <XCircle className="w-5 h-5" />}
                {workflowStatus === 'idle' && <Clock className="w-5 h-5" />}
              </div>
              <div>
                <h3 className="font-semibold text-lg">Temu流量加速自动化任务</h3>
                <p className="text-white/80 text-sm">
                  {workflowStatus === 'idle' && '准备开始执行...'}
                  {workflowStatus === 'running' && `正在执行第 ${currentStep + 1} 步，共 ${steps.length} 步`}
                  {workflowStatus === 'completed' && '任务执行完成'}
                  {workflowStatus === 'failed' && `第 ${failedStep + 1} 步执行失败`}
                </p>
              </div>
            </div>

            {/* 进度指示器 */}
            {workflowStatus === 'running' && (
              <div className="text-right">
                <div className="text-sm text-white/80">进度</div>
                <div className="text-lg font-bold">{Math.round(((currentStep + 1) / steps.length) * 100)}%</div>
              </div>
            )}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="p-6">
          {/* 任务配置信息 */}
          <div className="mb-6 bg-blue-50 rounded-xl p-4 border border-blue-100">
            <div className="flex items-center mb-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
              <span className="font-medium text-gray-900">执行配置</span>
            </div>
            <p className="text-sm text-gray-700">高级流量加权档位，价格范围4-6美元，时效30天，预计处理200个商品</p>
          </div>

          {/* 重新设计的时间线 - 只在开始执行后显示 */}
          {workflowStatus !== 'idle' && (
            <div className="relative">
              {/* 主时间线 */}
              <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>

              {steps.map((step, index) => {
                const isActive = index === currentStep;
                const isCompleted = step.status === 'completed';
                const isFailed = step.status === 'failed';
                const isRunning = step.status === 'running';
                const isWaiting = step.status === 'waiting';

                return (
                  <div key={step.id} className="relative mb-8 last:mb-0">
                    {/* 连接线段 - 只有完成的步骤显示绿色 */}
                    {index < steps.length - 1 && (
                      <div
                        className={`absolute left-6 top-12 w-0.5 h-16 transition-all duration-700 ${
                          isCompleted ? 'bg-green-500' : 'bg-gray-200'
                        }`}
                      ></div>
                    )}

                    {/* 步骤容器 */}
                    <div className="flex items-start space-x-4 relative">
                      {/* 状态图标 */}
                      <div className="flex-shrink-0 relative z-10">
                        {/* 背景圆圈 */}
                        <div className="w-12 h-12 bg-white rounded-full border-4 border-gray-200 absolute inset-0"></div>

                        {/* 状态图标 */}
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 relative z-10 ${
                          isCompleted ? 'bg-green-500 border-4 border-green-200 shadow-lg' :
                          isFailed ? 'bg-red-500 border-4 border-red-200 shadow-lg' :
                          isRunning ? 'bg-blue-500 border-4 border-blue-200 shadow-lg animate-pulse' :
                          isWaiting ? 'bg-gray-100 border-4 border-gray-200' :
                          'bg-gray-100 border-4 border-gray-200'
                        }`}>
                          {isCompleted && <CheckCircle className="w-6 h-6 text-white" />}
                          {isFailed && <XCircle className="w-6 h-6 text-white" />}
                          {isRunning && <Loader2 className="w-6 h-6 text-white animate-spin" />}
                          {isWaiting && <div className="w-4 h-4 rounded-full bg-gray-400"></div>}
                        </div>
                      </div>

                      {/* 步骤内容 */}
                      <div className="flex-1 min-w-0 pt-2">
                        <div className="mb-3">
                          <div className="flex items-center justify-between">
                            <h4 className={`text-lg font-semibold transition-colors duration-300 ${
                              isCompleted ? 'text-green-700' :
                              isFailed ? 'text-red-700' :
                              isRunning ? 'text-blue-700' :
                              'text-gray-500'
                            }`}>
                              {step.name}
                            </h4>

                            {/* 状态标签 */}
                            <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                              isCompleted ? 'bg-green-100 text-green-700' :
                              isFailed ? 'bg-red-100 text-red-700' :
                              isRunning ? 'bg-blue-100 text-blue-700' :
                              'bg-gray-100 text-gray-500'
                            }`}>
                              {isCompleted && '已完成'}
                              {isFailed && '执行失败'}
                              {isRunning && '进行中'}
                              {isWaiting && '等待执行'}
                            </span>
                          </div>

                          {/* 失败状态的重试按钮 */}
                          {isFailed && (
                            <button
                              onClick={retryFromFailed}
                              disabled={isRunning}
                              className="mt-3 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                            >
                              <RefreshCw className="w-4 h-4" />
                              <span>重试此步骤</span>
                            </button>
                          )}
                        </div>

                        {/* 步骤描述 */}
                        <div className={`p-4 rounded-lg border transition-all duration-300 ${
                          isCompleted ? 'bg-green-50 border-green-200' :
                          isFailed ? 'bg-red-50 border-red-200' :
                          isRunning ? 'bg-blue-50 border-blue-200' :
                          'bg-gray-50 border-gray-200'
                        }`}>
                          <p className={`text-sm leading-relaxed ${
                            isCompleted ? 'text-green-700' :
                            isFailed ? 'text-red-700' :
                            isRunning ? 'text-blue-700' :
                            'text-gray-600'
                          }`}>
                            {getStepDescription(step)}
                          </p>

                          {/* 失败时的详细错误信息 */}
                          {isFailed && (
                            <div className="mt-3 p-3 bg-red-100 border border-red-200 rounded-lg">
                              <div className="flex items-start space-x-2">
                                <AlertCircle className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
                                <div>
                                  <p className="text-red-800 text-sm font-medium">错误详情</p>
                                  <p className="text-red-700 text-sm mt-1">
                                    执行失败，可能是网络连接问题或系统繁忙。点击"重试此步骤"继续执行，已完成的步骤不会重复执行。
                                  </p>
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}

          {/* 操作按钮区域 */}
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-center space-x-4">
              {workflowStatus === 'idle' && (
                <button
                  onClick={executeWorkflow}
                  disabled={isRunning}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-8 py-3 rounded-xl text-base font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-3"
                >
                  <Play className="w-5 h-5" />
                  <span>开始执行任务</span>
                </button>
              )}

              {workflowStatus === 'running' && (
                <div className="flex items-center space-x-4 bg-gradient-to-r from-blue-50 to-cyan-50 rounded-xl px-6 py-4 border border-blue-200/50 shadow-sm">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full flex items-center justify-center">
                    <Loader2 className="w-5 h-5 text-white animate-spin" />
                  </div>
                  <div>
                    <div className="text-blue-700 font-medium">任务执行中...</div>
                    <div className="text-blue-600 text-sm">请耐心等待，预计还需 {Math.max(0, (steps.length - currentStep - 1) * 2)} 分钟</div>
                  </div>
                </div>
              )}

              {(workflowStatus === 'completed' || workflowStatus === 'failed') && (
                <div className="flex items-center space-x-3">
                  <button
                    onClick={resetWorkflow}
                    className="bg-gradient-to-r from-gray-500 to-gray-600 hover:from-gray-600 hover:to-gray-700 text-white px-6 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:shadow-lg hover:-translate-y-0.5 active:translate-y-0 flex items-center space-x-2"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>重新开始</span>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* 执行结果报告 */}
          {workflowStatus === 'completed' && executionResults && (
            <div className="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200/50 rounded-xl p-6 shadow-sm">
              <div className="flex items-center space-x-3 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                  <CheckCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-green-700 font-bold text-xl">🎉 任务执行完成！</h3>
                  <p className="text-green-600 text-sm">执行时间：{executionResults.executionTime}</p>
                </div>
              </div>

              {/* 执行统计 */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                <div className="bg-white rounded-lg p-4 text-center border border-green-200">
                  <div className="text-2xl font-bold text-green-700">{executionResults.totalProcessed}</div>
                  <div className="text-sm text-green-600">总处理数</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center border border-green-200">
                  <div className="text-2xl font-bold text-green-700">{executionResults.successful}</div>
                  <div className="text-sm text-green-600">成功处理</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center border border-green-200">
                  <div className="text-2xl font-bold text-orange-600">{executionResults.failed}</div>
                  <div className="text-sm text-orange-600">跳过/失败</div>
                </div>
                <div className="bg-white rounded-lg p-4 text-center border border-green-200">
                  <div className="text-2xl font-bold text-blue-700">${executionResults.totalCost}</div>
                  <div className="text-sm text-blue-600">总费用</div>
                </div>
              </div>

              {/* 详细报告 */}
              <div className="bg-white rounded-lg p-5 border border-green-200">
                <h4 className="font-semibold text-gray-900 mb-4">执行详情报告</h4>

                <div className="space-y-4">
                  <div>
                    <p className="text-gray-800 leading-relaxed">
                      本次任务执行完毕。共成功处理 <span className="font-semibold text-green-700">{executionResults.successful}</span> 个商品。
                      另有 <span className="font-semibold text-orange-600">{executionResults.failed}</span> 个商品未能成功，原因如下：
                    </p>
                  </div>

                  {/* 失败商品详情 */}
                  {executionResults.failedProducts.length > 0 && (
                    <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                      <h5 className="font-medium text-orange-800 mb-3">未成功处理的商品：</h5>
                      <ul className="space-y-2">
                        {executionResults.failedProducts.map((product, index) => (
                          <li key={index} className="text-sm text-orange-700">
                            <span className="font-medium">{product.name}</span> 因 {product.reason}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* 成功商品示例 */}
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <h5 className="font-medium text-green-800 mb-3">成功处理的商品示例：</h5>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                      {executionResults.successfulProducts.map((product, index) => (
                        <div key={index} className="text-sm text-green-700 bg-white rounded px-3 py-2 border border-green-200">
                          {product}
                        </div>
                      ))}
                    </div>
                    <p className="text-xs text-green-600 mt-2">以及其他 {executionResults.successful - executionResults.successfulProducts.length} 个商品...</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 失败状态提示 */}
          {workflowStatus === 'failed' && failedStep !== null && (
            <div className="mt-8 bg-gradient-to-r from-red-50 to-orange-50 border border-red-200/50 rounded-xl p-6 shadow-sm">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg">
                  <AlertCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="text-red-700 font-bold text-lg mb-1">
                    任务执行中断
                  </div>
                  <div className="text-red-600 text-sm">
                    第 {failedStep + 1} 步 "{steps[failedStep]?.name}" 执行失败，请点击对应步骤的"重试此步骤"按钮继续执行
                  </div>
                  <div className="text-red-500 text-xs mt-2">
                    💡 重试将从失败步骤开始，已完成的步骤不会重复执行
                  </div>
                </div>
              </div>
            </div>
          )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TemuWorkflowDemo;
