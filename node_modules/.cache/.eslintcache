[{"/Users/<USER>/Documents/augment-projects/ts/src/index.js": "1", "/Users/<USER>/Documents/augment-projects/ts/src/App.jsx": "2", "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx": "3", "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx": "4"}, {"size": 254, "mtime": 1751939357501, "results": "5", "hashOfConfig": "6"}, {"size": 231, "mtime": 1751939289220, "results": "7", "hashOfConfig": "6"}, {"size": 16977, "mtime": 1751941604103, "results": "8", "hashOfConfig": "6"}, {"size": 19594, "mtime": 1751942097956, "results": "9", "hashOfConfig": "6"}, {"filePath": "10", "messages": "11", "suppressedMessages": "12", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1200vd8", {"filePath": "13", "messages": "14", "suppressedMessages": "15", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/augment-projects/ts/src/index.js", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/App.jsx", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowShowcase.jsx", [], [], "/Users/<USER>/Documents/augment-projects/ts/src/TemuWorkflowDemo.jsx", [], []]